<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Paiement extends Model
{
    use HasFactory;

    protected $table = 'paiements';
    protected $primaryKey = 'idpaiement';

    protected $fillable = [
        'idcommande',
        'montant_paye',
        'mode_paiement',
        'date_paiement',
        'statut_paiement',
        'transaction_id',
        'reference_paiement',
    ];

    protected $casts = [
        'montant_paye' => 'decimal:2',
        'date_paiement' => 'date',
    ];

    public $timestamps = false;

    /**
     * Get the order for the payment.
     */
    public function commande()
    {
        return $this->belongsTo(Commande::class, 'idcommande', 'idcommande');
    }

    /**
     * Get payment method in French.
     */
    public function getMethodLabelAttribute(): string
    {
        $labels = [
            'virement' => 'Virement bancaire',
            'espece' => 'Espèces',
            'cheque' => 'Chèque',
            'carte' => 'Carte bancaire',
            'paypal' => 'PayPal',
            'stripe' => 'Stripe',
        ];

        return $labels[$this->mode_paiement] ?? $this->mode_paiement;
    }

    /**
     * Get payment status in French.
     */
    public function getStatusLabelAttribute(): string
    {
        $labels = [
            'paye' => 'Payé',
            'en_attente' => 'En attente',
            'echec' => 'Échec',
            'rembourse' => 'Remboursé',
        ];

        return $labels[$this->statut_paiement] ?? $this->statut_paiement;
    }

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->statut_paiement === 'paye';
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->statut_paiement === 'en_attente';
    }

    /**
     * Check if payment failed.
     */
    public function isFailed(): bool
    {
        return $this->statut_paiement === 'echec';
    }

    /**
     * Check if payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->statut_paiement === 'rembourse';
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->montant_paye, 2) . ' €';
    }

    /**
     * Scope a query to only include completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('statut_paiement', 'paye');
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('statut_paiement', 'en_attente');
    }
}
