import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowRight, Package } from 'lucide-react';

const CategoryCard = ({ category, className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className={`group relative overflow-hidden rounded-2xl card-hover ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link to={`/products?category=${category.id}`} className="block">
        {/* Background Image */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <motion.img
            src={category.image_url || '/images/categories/default.jpg'}
            alt={category.nom}
            className="w-full h-full object-cover"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.3 }}
          />
          
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          
          {/* Hover Overlay */}
          <motion.div
            className="absolute inset-0 bg-primary-600/20"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />

          {/* Content */}
          <div className="absolute inset-0 p-6 flex flex-col justify-end">
            {/* Category Icon */}
            <motion.div
              className="inline-flex items-center justify-center w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl mb-4"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.3 }}
            >
              <Package className="w-6 h-6 text-white" />
            </motion.div>

            {/* Category Name */}
            <motion.h3
              className="text-xl font-bold text-white mb-2 group-hover:text-primary-300 transition-colors"
              initial={{ y: 10, opacity: 0.8 }}
              animate={{ y: isHovered ? 0 : 10, opacity: isHovered ? 1 : 0.8 }}
              transition={{ duration: 0.3 }}
            >
              {category.nom}
            </motion.h3>

            {/* Description */}
            <motion.p
              className="text-white/80 text-sm mb-4 line-clamp-2"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: isHovered ? 0 : 10, opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              {category.description}
            </motion.p>

            {/* Products Count & Arrow */}
            <div className="flex items-center justify-between">
              <motion.span
                className="text-white/70 text-sm"
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: isHovered ? 0 : 10, opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                {category.products_count || 0} produits
              </motion.span>

              <motion.div
                className="flex items-center space-x-1 text-primary-300"
                initial={{ x: -10, opacity: 0 }}
                animate={{ x: isHovered ? 0 : -10, opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <span className="text-sm font-medium">Explorer</span>
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </div>
          </div>

          {/* Decorative Elements */}
          <motion.div
            className="absolute top-4 right-4 w-8 h-8 border-2 border-white/30 rounded-full"
            animate={{ rotate: isHovered ? 180 : 0 }}
            transition={{ duration: 0.5 }}
          />
          
          <motion.div
            className="absolute top-6 right-6 w-4 h-4 bg-primary-400/50 rounded-full"
            animate={{ scale: isHovered ? 1.5 : 1 }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </Link>
    </motion.div>
  );
};

export default CategoryCard;
