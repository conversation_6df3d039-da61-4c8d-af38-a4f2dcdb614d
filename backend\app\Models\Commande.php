<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Commande extends Model
{
    use HasFactory;

    protected $table = 'commande';
    protected $primaryKey = 'idcommande';

    protected $fillable = [
        'idclient',
        'datecommande',
        'statut',
        'total',
        'frais_livraison',
    ];

    protected $casts = [
        'datecommande' => 'datetime',
        'total' => 'decimal:2',
        'frais_livraison' => 'decimal:2',
    ];

    public $timestamps = false;

    /**
     * Get the client that owns the order.
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'idclient', 'idclient');
    }

    /**
     * Get the order details.
     */
    public function details()
    {
        return $this->hasMany(DetailCommande::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the delivery for the order.
     */
    public function livraison()
    {
        return $this->hasOne(Livraison::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the payment for the order.
     */
    public function paiement()
    {
        return $this->hasOne(Paiement::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the returns for the order.
     */
    public function retours()
    {
        return $this->hasMany(Retour::class, 'idcommande', 'idcommande');
    }

    /**
     * Scope a query to only include orders with specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('statut', $status);
    }

    /**
     * Scope a query to only include recent orders.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('datecommande', '>=', now()->subDays($days));
    }

    /**
     * Get the total amount including delivery fees.
     */
    public function getTotalWithDeliveryAttribute(): float
    {
        return $this->total + $this->frais_livraison;
    }

    /**
     * Get formatted total.
     */
    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_with_delivery, 2) . ' €';
    }

    /**
     * Check if order can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->statut, ['en_attente', 'confirme']);
    }

    /**
     * Check if order is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->statut === 'livre';
    }

    /**
     * Get order status in French.
     */
    public function getStatusLabelAttribute(): string
    {
        $labels = [
            'en_attente' => 'En attente',
            'confirme' => 'Confirmé',
            'annule' => 'Annulé',
            'expedie' => 'Expédié',
            'livre' => 'Livré',
        ];

        return $labels[$this->statut] ?? $this->statut;
    }

    /**
     * Get items count.
     */
    public function getItemsCountAttribute(): int
    {
        return $this->details()->sum('quantite');
    }
}
