@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Global Styles for OrderSync */
:root {
  --primary-gradient: linear-gradient(
    135deg,
    #0ea5e9 0%,
    #3b82f6 50%,
    #8b5cf6 100%
  );
  --secondary-gradient: linear-gradient(
    135deg,
    #a855f7 0%,
    #ec4899 50%,
    #f59e0b 100%
  );
  --dark-gradient: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e293b 50%,
    #334155 100%
  );
  --neon-blue: #00d4ff;
  --neon-purple: #b347d9;
  --neon-pink: #ff47d9;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Poppins", "Inter", system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #1e293b;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Dark mode styles */
.dark body {
  color: #e2e8f0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

/* Glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

/* Neon effects */
.neon-text {
  text-shadow: 0 0 5px currentColor, 0 0 10px currentColor,
    0 0 15px currentColor;
}

.neon-border {
  box-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
}

.neon-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Gradient text */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0ea5e9, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0284c7, #7c3aed);
}

/* Loading spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(59, 130, 246, 0.3);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Particle background */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Button hover effects */
.btn-neon {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-neon::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-neon:hover::before {
  left: 100%;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none;
  }
}
