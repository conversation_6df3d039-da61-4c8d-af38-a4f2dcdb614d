<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * Get all active categories
     */
    public function index(): JsonResponse
    {
        try {
            $categories = Category::active()
                ->withCount(['produits as products_count' => function ($query) {
                    $query->where('quantite', '>', 0);
                }])
                ->orderBy('nom')
                ->get();

            $transformedCategories = $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'nom' => $category->nom,
                    'description' => $category->description,
                    'image_url' => $category->image_url,
                    'products_count' => $category->products_count,
                    'date_creation' => $category->date_creation,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedCategories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single category with products
     */
    public function show($id): JsonResponse
    {
        try {
            $category = Category::with(['produits' => function ($query) {
                $query->available()->orderBy('date_creation', 'desc');
            }])->findOrFail($id);

            $categoryData = [
                'id' => $category->id,
                'nom' => $category->nom,
                'description' => $category->description,
                'image_url' => $category->image_url,
                'date_creation' => $category->date_creation,
                'products' => $category->produits->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'nom' => $product->nom,
                        'description' => $product->description,
                        'prix' => $product->prix,
                        'formatted_price' => $product->formatted_price,
                        'image_url' => $product->image_url,
                        'rating' => $product->rating,
                        'quantite' => $product->quantite,
                        'featured' => $product->featured,
                    ];
                }),
            ];

            return response()->json([
                'success' => true,
                'data' => $categoryData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Get categories with featured products
     */
    public function withFeaturedProducts(): JsonResponse
    {
        try {
            $categories = Category::active()
                ->with(['produits' => function ($query) {
                    $query->featured()->available()->limit(4);
                }])
                ->get();

            $transformedCategories = $categories->filter(function ($category) {
                return $category->produits->count() > 0;
            })->map(function ($category) {
                return [
                    'id' => $category->id,
                    'nom' => $category->nom,
                    'description' => $category->description,
                    'image_url' => $category->image_url,
                    'featured_products' => $category->produits->map(function ($product) {
                        return [
                            'id' => $product->id,
                            'nom' => $product->nom,
                            'prix' => $product->prix,
                            'formatted_price' => $product->formatted_price,
                            'image_url' => $product->image_url,
                            'rating' => $product->rating,
                        ];
                    }),
                ];
            })->values();

            return response()->json([
                'success' => true,
                'data' => $transformedCategories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch categories with featured products',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
