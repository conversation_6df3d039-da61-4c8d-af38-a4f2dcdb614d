import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ShoppingCart, 
  Minus, 
  Plus, 
  Trash2, 
  ArrowRight,
  ShoppingBag
} from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import toast from 'react-hot-toast';

const Cart = () => {
  const navigate = useNavigate();
  const { 
    items, 
    isLoading, 
    fetchCart, 
    updateCartItem, 
    removeFromCart, 
    clearCart,
    getCartSummary 
  } = useCartStore();

  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  const handleQuantityChange = async (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      await removeFromCart(itemId);
    } else {
      await updateCartItem(itemId, newQuantity);
    }
  };

  const handleRemoveItem = async (itemId) => {
    await removeFromCart(itemId);
  };

  const handleClearCart = async () => {
    if (window.confirm('Êtes-vous sûr de vouloir vider votre panier?')) {
      await clearCart();
    }
  };

  const summary = getCartSummary();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Chargement du panier..." />
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <ShoppingBag className="w-24 h-24 text-white/30 mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-white mb-4">Votre panier est vide</h2>
          <p className="text-white/70 mb-8">
            Découvrez nos produits et ajoutez-les à votre panier
          </p>
          <Link to="/products">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-3 bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-semibold rounded-lg transition-all duration-300 btn-neon"
            >
              Commencer mes achats
            </motion.button>
          </Link>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold gradient-text mb-4">
            Mon Panier
          </h1>
          <p className="text-white/70">
            {summary.totalItems} article{summary.totalItems > 1 ? 's' : ''} dans votre panier
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-4"
            >
              {items.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="glass-dark p-6 rounded-2xl"
                >
                  <div className="flex flex-col sm:flex-row gap-4">
                    {/* Product Image */}
                    <div className="w-full sm:w-24 h-48 sm:h-24 rounded-lg overflow-hidden bg-white/5 flex-shrink-0">
                      <img
                        src={item.product.image_url}
                        alt={item.product.nom}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                        <div className="flex-1">
                          <h3 className="text-white font-semibold mb-1">
                            <Link 
                              to={`/products/${item.product.id}`}
                              className="hover:text-primary-300 transition-colors"
                            >
                              {item.product.nom}
                            </Link>
                          </h3>
                          <p className="text-white/60 text-sm mb-2">
                            {item.product.category}
                          </p>
                          <p className="text-white font-medium">
                            {item.product.formatted_price}
                          </p>
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                          <div className="flex items-center space-x-2 bg-white/10 rounded-lg p-1">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="p-1 text-white hover:text-primary-300 transition-colors"
                            >
                              <Minus className="w-4 h-4" />
                            </motion.button>
                            <span className="w-8 text-center text-white font-medium">
                              {item.quantity}
                            </span>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              disabled={item.quantity >= item.product.quantite}
                              className="p-1 text-white hover:text-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              <Plus className="w-4 h-4" />
                            </motion.button>
                          </div>

                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleRemoveItem(item.id)}
                            className="p-2 text-red-400 hover:text-red-300 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </div>

                      {/* Stock Warning */}
                      {!item.product.in_stock && (
                        <div className="mt-2 text-red-400 text-sm">
                          ⚠️ Ce produit n'est plus en stock
                        </div>
                      )}

                      {/* Total Price */}
                      <div className="mt-4 text-right">
                        <span className="text-lg font-bold text-white">
                          {item.formatted_total}
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}

              {/* Clear Cart Button */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-center pt-4"
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleClearCart}
                  className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center space-x-2 mx-auto"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Vider le panier</span>
                </motion.button>
              </motion.div>
            </motion.div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="glass-dark p-6 rounded-2xl sticky top-24"
            >
              <h2 className="text-xl font-bold text-white mb-6">Résumé de la commande</h2>

              <div className="space-y-4 mb-6">
                <div className="flex justify-between text-white/80">
                  <span>Sous-total ({summary.totalItems} articles)</span>
                  <span>{summary.formattedSubtotal}</span>
                </div>
                
                <div className="flex justify-between text-white/80">
                  <span>Frais de livraison</span>
                  <span>{summary.formattedDeliveryFee}</span>
                </div>

                <div className="border-t border-white/10 pt-4">
                  <div className="flex justify-between text-lg font-bold text-white">
                    <span>Total</span>
                    <span>{summary.formattedTotal}</span>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigate('/checkout')}
                className="w-full py-4 bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-semibold rounded-lg transition-all duration-300 btn-neon flex items-center justify-center space-x-2"
              >
                <span>Procéder au paiement</span>
                <ArrowRight className="w-5 h-5" />
              </motion.button>

              {/* Continue Shopping */}
              <Link to="/products">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full mt-4 py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors"
                >
                  Continuer mes achats
                </motion.button>
              </Link>

              {/* Security Info */}
              <div className="mt-6 pt-6 border-t border-white/10">
                <div className="flex items-center space-x-2 text-white/60 text-sm">
                  <ShoppingCart className="w-4 h-4" />
                  <span>Paiement 100% sécurisé</span>
                </div>
                <div className="mt-2 text-white/50 text-xs">
                  Vos données sont protégées par un cryptage SSL
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
