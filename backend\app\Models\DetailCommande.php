<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DetailCommande extends Model
{
    use HasFactory;

    protected $table = 'detailcommande';
    protected $primaryKey = 'iddetailcommande';

    protected $fillable = [
        'idcommande',
        'idproduit',
        'quantite',
        'prixunitaire',
    ];

    protected $casts = [
        'quantite' => 'integer',
        'prixunitaire' => 'decimal:2',
    ];

    public $timestamps = false;

    /**
     * Get the order that owns the detail.
     */
    public function commande()
    {
        return $this->belongsTo(Commande::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the product for the detail.
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class, 'idproduit');
    }

    /**
     * Get the total price for this detail.
     */
    public function getTotalPriceAttribute(): float
    {
        return $this->quantite * $this->prixunitaire;
    }

    /**
     * Get formatted total price.
     */
    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_price, 2) . ' €';
    }

    /**
     * Get formatted unit price.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return number_format($this->prixunitaire, 2) . ' €';
    }
}
