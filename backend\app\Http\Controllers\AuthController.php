<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Client;
use App\Models\Log;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    /**
     * Register a new user
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|string|email|max:100|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'in:client,delivery',
            'nomclient' => 'required_if:role,client|string|max:255',
            'adresse' => 'required_if:role,client|string|max:255',
            'tel' => 'required_if:role,client|string|max:20',
            'ville' => 'required_if:role,client|string|max:100',
            'code_postal' => 'required_if:role,client|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create user
            $user = User::create([
                'username' => $request->username,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role ?? 'client',
            ]);

            // Create client profile if role is client
            if ($user->role === 'client') {
                Client::create([
                    'user_id' => $user->id,
                    'nomclient' => $request->nomclient,
                    'adresse' => $request->adresse,
                    'tel' => $request->tel,
                    'ville' => $request->ville,
                    'code_postal' => $request->code_postal,
                ]);
            }

            // Create access token
            $token = $user->createToken('auth_token')->plainTextToken;

            // Log the registration
            Log::createLog(
                $user->id,
                'create',
                'users',
                $user->id,
                'User registered: ' . $user->username,
                $request->ip(),
                $request->userAgent()
            );

            return response()->json([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'role' => $user->role,
                    ],
                    'token' => $token,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = User::where('email', $request->email)->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            // Update last login
            $user->update(['last_login' => now()]);

            // Create access token
            $token = $user->createToken('auth_token')->plainTextToken;

            // Log the login
            Log::createLog(
                $user->id,
                'login',
                'users',
                $user->id,
                'User logged in: ' . $user->username,
                $request->ip(),
                $request->userAgent()
            );

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'role' => $user->role,
                        'last_login' => $user->last_login,
                    ],
                    'token' => $token,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            // Log the logout
            Log::createLog(
                $user->id,
                'logout',
                'users',
                $user->id,
                'User logged out: ' . $user->username,
                $request->ip(),
                $request->userAgent()
            );

            // Revoke current token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logout successful'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Logout failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $userData = [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'role' => $user->role,
                'last_login' => $user->last_login,
                'created_at' => $user->created_at,
            ];

            // Add client profile if exists
            if ($user->role === 'client' && $user->client) {
                $userData['client'] = [
                    'nomclient' => $user->client->nomclient,
                    'adresse' => $user->client->adresse,
                    'tel' => $user->client->tel,
                    'ville' => $user->client->ville,
                    'code_postal' => $user->client->code_postal,
                ];
            }

            // Add delivery profile if exists
            if ($user->role === 'delivery' && $user->livreur) {
                $userData['livreur'] = [
                    'nom' => $user->livreur->nom,
                    'prenom' => $user->livreur->prenom,
                    'adresse' => $user->livreur->adresse,
                    'tel' => $user->livreur->tel,
                    'vehicule' => $user->livreur->vehicule,
                    'zone_livraison' => $user->livreur->zone_livraison,
                    'disponible' => $user->livreur->disponible,
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $userData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get user data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh token
     */
    public function refresh(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            // Revoke current token
            $request->user()->currentAccessToken()->delete();
            
            // Create new token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Token refreshed successfully',
                'data' => [
                    'token' => $token,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token refresh failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
