<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Livraison extends Model
{
    use HasFactory;

    protected $table = 'livraisons';
    protected $primaryKey = 'idlivraison';

    protected $fillable = [
        'idcommande',
        'idlivreur',
        'date_expedition',
        'date_livraison_estimee',
        'date_livraison_reelle',
        'statut_livraison',
        'transporteur',
        'adresse_livraison',
        'commentaire',
    ];

    protected $casts = [
        'date_expedition' => 'date',
        'date_livraison_estimee' => 'date',
        'date_livraison_reelle' => 'date',
    ];

    public $timestamps = false;

    /**
     * Get the order for the delivery.
     */
    public function commande()
    {
        return $this->belongsTo(Commande::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the delivery person for the delivery.
     */
    public function livreur()
    {
        return $this->belongsTo(Livreur::class, 'idlivreur');
    }

    /**
     * Scope a query to only include deliveries with specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('statut_livraison', $status);
    }

    /**
     * Scope a query to only include active deliveries.
     */
    public function scopeActive($query)
    {
        return $query->where('statut_livraison', 'en_cours');
    }

    /**
     * Get delivery status in French.
     */
    public function getStatusLabelAttribute(): string
    {
        $labels = [
            'en_cours' => 'En cours',
            'livre' => 'Livré',
            'retarde' => 'Retardé',
            'annule' => 'Annulé',
        ];

        return $labels[$this->statut_livraison] ?? $this->statut_livraison;
    }

    /**
     * Check if delivery is completed.
     */
    public function isCompleted(): bool
    {
        return $this->statut_livraison === 'livre';
    }

    /**
     * Check if delivery is delayed.
     */
    public function isDelayed(): bool
    {
        return $this->statut_livraison === 'retarde' || 
               ($this->statut_livraison === 'en_cours' && 
                $this->date_livraison_estimee < now()->toDateString());
    }

    /**
     * Get estimated delivery time remaining.
     */
    public function getTimeRemainingAttribute(): ?int
    {
        if ($this->isCompleted()) return null;
        
        return now()->diffInDays($this->date_livraison_estimee, false);
    }

    /**
     * Get delivery progress percentage.
     */
    public function getProgressPercentageAttribute(): int
    {
        switch ($this->statut_livraison) {
            case 'en_cours':
                return 50;
            case 'livre':
                return 100;
            case 'retarde':
                return 25;
            case 'annule':
                return 0;
            default:
                return 0;
        }
    }
}
