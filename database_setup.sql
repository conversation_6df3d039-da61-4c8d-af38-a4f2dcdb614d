-- php<PERSON>y<PERSON><PERSON><PERSON> SQL Dump
-- OrderSync Database Setup
-- Database: `bdliv`

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

--
-- Database: `bdliv`
--
CREATE DATABASE IF NOT EXISTS `bdliv` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `bdliv`;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','client','delivery') NOT NULL DEFAULT 'client',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--
INSERT IGNORE INTO `users` (`username`, `email`, `password`, `role`) VALUES
('admin1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('client1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'client'),
('delivery1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'delivery');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--
CREATE TABLE IF NOT EXISTS `sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `date_creation` date DEFAULT NULL,
  `active` tinyint(1) DEFAULT 1,
  `image_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--
INSERT IGNORE INTO `categories` (`id`, `nom`, `description`, `date_creation`, `active`, `image_url`) VALUES
(1, 'Électronique', 'Produits électroniques comme les téléphones, ordinateurs, etc.', '2023-12-12', 1, 'images/categories/electronics.jpg'),
(2, 'Vêtements', 'Habillement pour hommes, femmes et enfants.', '2023-12-12', 1, 'images/categories/clothing.jpg'),
(3, 'Maison et Cuisine', 'Articles pour la maison et la cuisine.', '2023-12-12', 1, 'images/categories/home.jpg'),
(4, 'Sport et Loisirs', 'Équipements de sport et activités de loisir.', '2023-12-12', 1, 'images/categories/sports.jpg'),
(5, 'Livres', 'Livres, ebooks et magazines.', '2023-12-12', 1, 'images/categories/books.jpg'),
(6, 'Jouets', 'Jouets pour enfants de tout âge.', '2023-12-12', 1, 'images/categories/toys.jpg'),
(7, 'Beauté et Santé', 'Produits cosmétiques et soins personnels.', '2024-12-10', 1, 'images/categories/beauty.jpg');

-- --------------------------------------------------------

--
-- Table structure for table `produits`
--
CREATE TABLE IF NOT EXISTS `produits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(150) NOT NULL,
  `description` varchar(500) DEFAULT NULL,
  `prix` decimal(10,2) NOT NULL,
  `quantite` int(11) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `date_creation` date DEFAULT NULL,
  `id_categorie` int(11) NOT NULL,
  `featured` tinyint(1) DEFAULT 0,
  `rating` decimal(3,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`id_categorie`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  INDEX `idx_nom` (`nom`),
  INDEX `idx_featured` (`featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `produits`
--
INSERT IGNORE INTO `produits` (`id`, `nom`, `description`, `prix`, `quantite`, `image_url`, `date_creation`, `id_categorie`, `featured`, `rating`) VALUES
(1, 'Smartphone Samsung Galaxy S23', 'Téléphone Android performant avec 256 Go de stockage et écran AMOLED 6.1 pouces.', 900.00, 20, 'images/products/samsung_s23.jpg', '2023-12-15', 1, 1, 4.5),
(2, 'Ordinateur Portable Dell XPS 13', 'Ultrabook puissant avec écran tactile 13.3 pouces, Intel Core i7, 16GB RAM.', 1300.00, 30, 'images/products/dell_xps13.jpg', '2023-12-15', 1, 1, 4.7),
(3, 'Casque Audio Sony WH-1000XM5', 'Casque sans fil avec réduction de bruit active et autonomie 30h.', 300.00, 100, 'images/products/sony_wh1000xm5.jpg', '2023-12-15', 1, 0, 4.8),
(4, 'T-shirt Homme en Coton Bio', 'T-shirt confortable en coton bio, disponible en plusieurs tailles et couleurs.', 20.00, 200, 'images/products/tshirt_coton.jpg', '2023-12-15', 2, 0, 4.2),
(5, 'Robe d\'Été Fleurie', 'Robe légère idéale pour les journées chaudes, motif floral élégant.', 40.00, 150, 'images/products/robe_fleurie.jpg', '2023-12-15', 2, 1, 4.3),
(6, 'Jeans Slim Femme', 'Jean ajusté disponible en bleu clair et foncé, coupe moderne.', 50.00, 100, 'images/products/jeans_femme.jpg', '2023-12-15', 2, 0, 4.1),
(7, 'Mixeur Plongeant Philips', 'Mixeur puissant pour préparer des soupes et smoothies, 800W.', 60.00, 80, 'images/products/mixeur_philips.jpg', '2023-12-15', 3, 0, 4.4),
(8, 'Batterie de Cuisine Tefal', 'Set de casseroles et poêles avec revêtement antiadhésif Titanium.', 100.00, 60, 'images/products/tefal_cuisine.jpg', '2023-12-15', 3, 1, 4.6),
(9, 'Aspirateur Dyson V15', 'Aspirateur sans fil avec grande autonomie et technologie laser.', 600.00, 20, 'images/products/dyson_v15.jpg', '2023-12-15', 3, 1, 4.9),
(10, 'Vélo de Montagne Rockrider', 'Vélo tout terrain avec 21 vitesses et suspension avant.', 500.00, 15, 'images/products/rockrider_velo.jpg', '2023-12-15', 4, 0, 4.3),
(11, 'Haltères Ajustables 20 kg', 'Set d\'haltères idéal pour la musculation à domicile, réglables.', 80.00, 50, 'images/products/halteres_20kg.jpg', '2023-12-15', 4, 0, 4.2),
(12, 'Raquette de Tennis Babolat', 'Raquette légère et ergonomique pour joueurs intermédiaires.', 120.00, 40, 'images/products/babolat_raquette.jpg', '2023-12-15', 4, 0, 4.5),
(13, 'Le Seigneur des Anneaux - Tome 1', 'Roman fantastique de J.R.R. Tolkien, édition collector.', 15.00, 200, 'images/products/lotr_tome1.jpg', '2023-12-15', 5, 0, 4.8),
(14, 'Python pour les Nuls', 'Guide d\'apprentissage pour débuter en programmation Python.', 20.00, 150, 'images/products/python_nuls.jpg', '2023-12-15', 5, 0, 4.4),
(15, 'Cuisine Facile pour Tous', 'Livre de recettes simples et rapides pour débutants.', 10.00, 100, 'images/products/cuisine_facile.jpg', '2023-12-15', 5, 0, 4.1),
(16, 'Lego City - Caserne de Pompiers', 'Set de construction Lego pour les enfants de 6-12 ans.', 90.00, 60, 'images/products/lego_caserne.jpg', '2023-12-15', 6, 1, 4.7),
(17, 'Puzzle 1000 Pièces', 'Puzzle pour adultes et enfants, motif paysage montagnard.', 20.00, 100, 'images/products/puzzle_1000.jpg', '2023-12-15', 6, 0, 4.3),
(18, 'Jeu de Société Monopoly', 'Version classique du célèbre jeu de société familial.', 30.00, 150, 'images/products/monopoly.jpg', '2023-12-15', 6, 0, 4.5),
(19, 'Crème Hydratante Nivea', 'Crème hydratante pour tous types de peau, 200ml.', 8.00, 500, 'images/products/nivea_creme.jpg', '2023-12-15', 7, 0, 4.2),
(20, 'Brosse à Dents Électrique Oral-B', 'Brosse à dents électrique rechargeable avec 3 modes.', 50.00, 100, 'images/products/oralb_brosse.jpg', '2023-12-15', 7, 0, 4.6);

-- --------------------------------------------------------

--
-- Table structure for table `client`
--
CREATE TABLE IF NOT EXISTS `client` (
  `idclient` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `nomclient` varchar(255) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `tel` varchar(20) DEFAULT NULL,
  `ville` varchar(100) DEFAULT NULL,
  `code_postal` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`idclient`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `client`
--
INSERT IGNORE INTO `client` (`idclient`, `user_id`, `nomclient`, `adresse`, `tel`, `ville`, `code_postal`) VALUES
(1, 2, 'Jean Dupont', '123 Rue de la Paix', '0123456789', 'Paris', '75001'),
(2, NULL, 'Marie Martin', '456 Avenue des Fleurs', '0987654321', 'Lyon', '69001'),
(3, NULL, 'Pierre Dubois', '789 Boulevard du Soleil', '0654321098', 'Marseille', '13001'),
(4, NULL, 'Anne Lefevre', '321 Rue de la Liberté', '0789456123', 'Lille', '59000'),
(5, NULL, 'Jacques Robert', '654 Avenue de la République', '0543219876', 'Bordeaux', '33000');

-- --------------------------------------------------------

--
-- Table structure for table `commande`
--
CREATE TABLE IF NOT EXISTS `commande` (
  `idcommande` int(11) NOT NULL AUTO_INCREMENT,
  `idclient` int(11) NOT NULL,
  `datecommande` datetime DEFAULT CURRENT_TIMESTAMP,
  `statut` enum('en_attente','confirme','annule','expedie','livre') DEFAULT 'en_attente',
  `total` decimal(10,2) DEFAULT 0.00,
  `frais_livraison` decimal(10,2) DEFAULT 10.00,
  PRIMARY KEY (`idcommande`),
  FOREIGN KEY (`idclient`) REFERENCES `client` (`idclient`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `commande`
--
INSERT IGNORE INTO `commande` (`idcommande`, `idclient`, `datecommande`, `statut`, `total`, `frais_livraison`) VALUES
(1, 1, '2025-01-01 10:00:00', 'confirme', 2100.00, 10.00),
(2, 2, '2025-01-02 12:00:00', 'expedie', 180.00, 10.00),
(3, 1, '2025-01-05 14:00:00', 'en_attente', 740.00, 10.00),
(4, 3, '2025-01-03 09:00:00', 'confirme', 130.00, 10.00),
(5, 4, '2025-01-04 11:00:00', 'annule', 180.00, 10.00);

-- --------------------------------------------------------

--
-- Table structure for table `detailcommande`
--
CREATE TABLE IF NOT EXISTS `detailcommande` (
  `iddetailcommande` int(11) NOT NULL AUTO_INCREMENT,
  `idcommande` int(11) NOT NULL,
  `idproduit` int(11) NOT NULL,
  `quantite` int(11) NOT NULL,
  `prixunitaire` decimal(10,2) NOT NULL,
  PRIMARY KEY (`iddetailcommande`),
  FOREIGN KEY (`idcommande`) REFERENCES `commande` (`idcommande`) ON DELETE CASCADE,
  FOREIGN KEY (`idproduit`) REFERENCES `produits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `detailcommande`
--
INSERT IGNORE INTO `detailcommande` (`iddetailcommande`, `idcommande`, `idproduit`, `quantite`, `prixunitaire`) VALUES
(1, 1, 1, 2, 900.00),
(2, 1, 3, 1, 300.00),
(3, 2, 5, 3, 40.00),
(4, 2, 7, 1, 60.00),
(5, 3, 10, 1, 500.00),
(6, 3, 12, 2, 120.00),
(7, 4, 14, 5, 20.00),
(8, 4, 18, 1, 30.00),
(9, 5, 19, 10, 8.00),
(10, 5, 20, 2, 50.00);

-- --------------------------------------------------------

--
-- Table structure for table `livreur`
--
CREATE TABLE IF NOT EXISTS `livreur` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `nom` varchar(100) NOT NULL,
  `prenom` varchar(100) NOT NULL,
  `adresse` varchar(100) NOT NULL,
  `tel` varchar(20) NOT NULL,
  `vehicule` varchar(50) DEFAULT NULL,
  `zone_livraison` varchar(100) DEFAULT NULL,
  `disponible` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `livreur`
--
INSERT IGNORE INTO `livreur` (`id`, `user_id`, `nom`, `prenom`, `adresse`, `tel`, `vehicule`, `zone_livraison`, `disponible`) VALUES
(1, 3, 'Dupont', 'Jean', '10 Rue des Lilas, Paris', '0601020304', 'Moto', 'Paris Centre', 1),
(2, NULL, 'Martin', 'Claire', '25 Avenue Victor Hugo, Lyon', '0611121314', 'Vélo', 'Lyon 1-3', 1),
(3, NULL, 'Nguyen', 'Paul', '12 Boulevard Haussmann, Marseille', '0622232425', 'Voiture', 'Marseille Nord', 1),
(4, NULL, 'Lopez', 'Sofia', '5 Place de la République, Bordeaux', '0633343536', 'Moto', 'Bordeaux Centre', 0),
(5, NULL, 'Morel', 'Luc', '18 Allée des Cerisiers, Toulouse', '0644454647', 'Vélo', 'Toulouse Sud', 1);

-- --------------------------------------------------------

--
-- Table structure for table `livraisons`
--
CREATE TABLE IF NOT EXISTS `livraisons` (
  `idlivraison` int(11) NOT NULL AUTO_INCREMENT,
  `idcommande` int(11) NOT NULL,
  `idlivreur` int(11) NOT NULL,
  `date_expedition` date NOT NULL,
  `date_livraison_estimee` date NOT NULL,
  `date_livraison_reelle` date DEFAULT NULL,
  `statut_livraison` enum('en_cours','livre','retarde','annule') NOT NULL,
  `transporteur` varchar(100) NOT NULL,
  `adresse_livraison` text NOT NULL,
  `commentaire` text DEFAULT NULL,
  PRIMARY KEY (`idlivraison`),
  FOREIGN KEY (`idcommande`) REFERENCES `commande` (`idcommande`) ON DELETE CASCADE,
  FOREIGN KEY (`idlivreur`) REFERENCES `livreur` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
