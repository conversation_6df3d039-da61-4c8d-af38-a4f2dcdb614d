-- OrderSync Database Setup - Part 2
-- Additional tables for bdliv database

USE `bdliv`;

--
-- Dumping data for table `livraisons`
--
INSERT IGNORE INTO `livraisons` (`idlivraison`, `idcommande`, `idlivreur`, `date_expedition`, `date_livraison_estimee`, `date_livraison_reelle`, `statut_livraison`, `transporteur`, `adresse_livrai<PERSON>`, `commentaire`) VALUES
(1, 1, 1, '2025-02-12', '2025-03-01', NULL, 'en_cours', 'OrderSync Express', '123 Rue de la Paix, Paris 75001', 'Livraison en cours'),
(2, 2, 2, '2025-01-15', '2025-02-25', '2025-02-24', 'livre', 'OrderSync Vélo', '456 Avenue des Fleurs, Lyon 69001', 'Livré avec succès'),
(3, 3, 3, '2025-03-12', '2025-03-25', NULL, 'en_cours', 'OrderSync Auto', '123 Rue de la Paix, Paris 75001', 'En route vers le client'),
(4, 5, 3, '2025-04-05', '2025-04-05', NULL, 'annule', 'OrderSync Auto', '321 Rue de la Liberté, Lille 59000', 'Commande annulée par le client');

-- --------------------------------------------------------

--
-- Table structure for table `paiements`
--
CREATE TABLE IF NOT EXISTS `paiements` (
  `idpaiement` int(11) NOT NULL AUTO_INCREMENT,
  `idcommande` int(11) NOT NULL,
  `montant_paye` decimal(10,2) NOT NULL,
  `mode_paiement` enum('virement','espece','cheque','carte','paypal','stripe') NOT NULL,
  `date_paiement` date NOT NULL,
  `statut_paiement` enum('paye','en_attente','echec','rembourse') NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `reference_paiement` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`idpaiement`),
  FOREIGN KEY (`idcommande`) REFERENCES `commande` (`idcommande`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `paiements`
--
INSERT IGNORE INTO `paiements` (`idpaiement`, `idcommande`, `montant_paye`, `mode_paiement`, `date_paiement`, `statut_paiement`, `transaction_id`, `reference_paiement`) VALUES
(1, 2, 180.00, 'carte', '2024-11-11', 'paye', 'TXN_001', 'REF_001'),
(2, 1, 2100.00, 'virement', '2025-03-28', 'paye', 'TXN_002', 'REF_002'),
(3, 3, 740.00, 'paypal', '2025-03-28', 'en_attente', 'TXN_003', 'REF_003'),
(4, 4, 130.00, 'carte', '2025-03-28', 'paye', 'TXN_004', 'REF_004'),
(5, 5, 180.00, 'stripe', '2025-02-28', 'rembourse', 'TXN_005', 'REF_005');

-- --------------------------------------------------------

--
-- Table structure for table `retours`
--
CREATE TABLE IF NOT EXISTS `retours` (
  `idretour` int(11) NOT NULL AUTO_INCREMENT,
  `idcommande` int(11) NOT NULL,
  `idproduit` int(11) NOT NULL,
  `quantite_retournee` int(11) NOT NULL,
  `raison` varchar(100) NOT NULL,
  `date_retour` date NOT NULL,
  `statut_retour` enum('accepte','refuse','en_attente','traite') NOT NULL,
  `commentaire_admin` text DEFAULT NULL,
  `montant_rembourse` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`idretour`),
  FOREIGN KEY (`idcommande`) REFERENCES `commande` (`idcommande`) ON DELETE CASCADE,
  FOREIGN KEY (`idproduit`) REFERENCES `produits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `retours`
--
INSERT IGNORE INTO `retours` (`idretour`, `idcommande`, `idproduit`, `quantite_retournee`, `raison`, `date_retour`, `statut_retour`, `commentaire_admin`, `montant_rembourse`) VALUES
(1, 1, 3, 1, 'Produit défectueux', '2025-04-01', 'accepte', 'Remboursement approuvé', 300.00),
(2, 2, 5, 1, 'Taille incorrecte', '2025-03-15', 'en_attente', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `logs`
--
CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` enum('create','update','delete','login','logout','view') NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `record_id` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `logs`
--
INSERT IGNORE INTO `logs` (`user_id`, `action`, `table_name`, `record_id`, `description`, `ip_address`, `created_at`) VALUES
(1, 'create', 'produits', 1, 'Ajout du produit Smartphone Samsung Galaxy S23', '127.0.0.1', '2023-12-15 10:00:00'),
(2, 'login', 'users', 2, 'Connexion de l\'utilisateur client1', '127.0.0.1', '2025-01-01 09:00:00'),
(1, 'update', 'commande', 1, 'Mise à jour du statut de la commande', '127.0.0.1', '2025-01-01 10:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('order','delivery','payment','system','promotion') DEFAULT 'system',
  `is_read` tinyint(1) DEFAULT 0,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `action_url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  INDEX `idx_user_read` (`user_id`, `is_read`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--
INSERT IGNORE INTO `notifications` (`user_id`, `title`, `message`, `type`, `is_read`, `priority`, `action_url`, `created_at`) VALUES
(2, 'Commande confirmée', 'Votre commande #1 a été confirmée et sera bientôt expédiée.', 'order', 0, 'medium', '/orders/1', '2025-01-01 10:30:00'),
(2, 'Livraison effectuée', 'Votre commande #2 a été livrée avec succès.', 'delivery', 1, 'high', '/orders/2', '2025-02-25 14:00:00'),
(1, 'Nouvelle commande', 'Une nouvelle commande #3 nécessite votre attention.', 'order', 0, 'high', '/admin/orders/3', '2025-01-05 14:05:00');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--
CREATE TABLE IF NOT EXISTS `cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_user_product` (`user_id`, `product_id`),
  UNIQUE KEY `unique_session_product` (`session_id`, `product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_images`
--
CREATE TABLE IF NOT EXISTS `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `is_primary` tinyint(1) DEFAULT 0,
  `sort_order` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`product_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--
CREATE TABLE IF NOT EXISTS `reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `rating` int(1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
  `comment` text DEFAULT NULL,
  `is_verified_purchase` tinyint(1) DEFAULT 0,
  `is_approved` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`product_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_user_product_review` (`user_id`, `product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- AUTO_INCREMENT for dumped tables
--
ALTER TABLE `users` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `sessions` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `categories` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
ALTER TABLE `produits` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;
ALTER TABLE `client` MODIFY `idclient` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `commande` MODIFY `idcommande` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `detailcommande` MODIFY `iddetailcommande` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;
ALTER TABLE `livreur` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `livraisons` MODIFY `idlivraison` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;
ALTER TABLE `paiements` MODIFY `idpaiement` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `retours` MODIFY `idretour` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
ALTER TABLE `logs` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `notifications` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `cart` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `product_images` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `reviews` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

COMMIT;
