import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useThemeStore = create(
  persist(
    (set, get) => ({
      isDark: true, // Default to dark theme for OrderSync
      
      // Toggle theme
      toggleTheme: () => {
        const { isDark } = get();
        const newTheme = !isDark;
        
        set({ isDark: newTheme });
        
        // Update document class
        if (newTheme) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
        
        return newTheme;
      },
      
      // Set theme explicitly
      setTheme: (isDark) => {
        set({ isDark });
        
        // Update document class
        if (isDark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },
      
      // Initialize theme
      initializeTheme: () => {
        const { isDark } = get();
        
        // Check system preference if no stored preference
        if (isDark === null) {
          const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          set({ isDark: systemPrefersDark });
          
          if (systemPrefersDark) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        } else {
          // Apply stored preference
          if (isDark) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        }
      },
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        isDark: state.isDark,
      }),
    }
  )
);

// Initialize theme on store creation
useThemeStore.getState().initializeTheme();

// Listen for system theme changes
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
  const { isDark } = useThemeStore.getState();
  
  // Only update if user hasn't set a preference
  if (isDark === null) {
    useThemeStore.getState().setTheme(e.matches);
  }
});

export { useThemeStore };
