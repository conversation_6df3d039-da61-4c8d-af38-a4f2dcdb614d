<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Livreur extends Model
{
    use HasFactory;

    protected $table = 'livreur';

    protected $fillable = [
        'user_id',
        'nom',
        'prenom',
        'adresse',
        'tel',
        'vehicule',
        'zone_livraison',
        'disponible',
    ];

    protected $casts = [
        'disponible' => 'boolean',
    ];

    public $timestamps = false;

    /**
     * Get the user that owns the delivery person profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the deliveries for the delivery person.
     */
    public function livraisons()
    {
        return $this->hasMany(Livraison::class, 'idlivreur');
    }

    /**
     * Get the full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->prenom . ' ' . $this->nom;
    }

    /**
     * Scope a query to only include available delivery persons.
     */
    public function scopeAvailable($query)
    {
        return $query->where('disponible', true);
    }

    /**
     * Scope a query to filter by zone.
     */
    public function scopeInZone($query, $zone)
    {
        return $query->where('zone_livraison', 'LIKE', "%{$zone}%");
    }

    /**
     * Get active deliveries count.
     */
    public function getActiveDeliveriesCountAttribute(): int
    {
        return $this->livraisons()
                    ->where('statut_livraison', 'en_cours')
                    ->count();
    }

    /**
     * Get completed deliveries count.
     */
    public function getCompletedDeliveriesCountAttribute(): int
    {
        return $this->livraisons()
                    ->where('statut_livraison', 'livre')
                    ->count();
    }

    /**
     * Get delivery success rate.
     */
    public function getSuccessRateAttribute(): float
    {
        $total = $this->livraisons()->count();
        if ($total === 0) return 0;

        $completed = $this->completed_deliveries_count;
        return ($completed / $total) * 100;
    }
}
