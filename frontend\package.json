{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/node": "^24.0.10", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "framer-motion": "^11.15.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "lucide-react": "^0.460.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "zustand": "^4.4.7", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "particles.js": "^2.0.0", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}