import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ShoppingCart, 
  Heart, 
  Star, 
  Eye, 
  Plus,
  Check,
  AlertCircle
} from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import toast from 'react-hot-toast';

const ProductCard = ({ product, className = '' }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isLiked, setIsLiked] = useState(false);

  const { addToCart, isInCart, getItemQuantity } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated()) {
      toast.error('Veuillez vous connecter pour ajouter des produits au panier');
      return;
    }

    if (!product.in_stock) {
      toast.error('Ce produit n\'est plus en stock');
      return;
    }

    setIsAddingToCart(true);
    
    try {
      const result = await addToCart(product.id, 1);
      if (result.success) {
        toast.success('Produit ajouté au panier!');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleToggleLike = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isAuthenticated()) {
      toast.error('Veuillez vous connecter pour ajouter aux favoris');
      return;
    }

    setIsLiked(!isLiked);
    toast.success(isLiked ? 'Retiré des favoris' : 'Ajouté aux favoris');
  };

  const inCart = isInCart(product.id);
  const cartQuantity = getItemQuantity(product.id);

  return (
    <motion.div
      className={`group relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden card-hover ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link to={`/products/${product.id}`} className="block">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden">
          <motion.img
            src={product.image_url || '/images/products/default.jpg'}
            alt={product.nom}
            className="w-full h-full object-cover"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.3 }}
          />
          
          {/* Overlay */}
          <motion.div
            className="absolute inset-0 bg-black/20"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />

          {/* Featured Badge */}
          {product.featured && (
            <div className="absolute top-3 left-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
              Vedette
            </div>
          )}

          {/* Stock Status */}
          {!product.in_stock && (
            <div className="absolute top-3 right-3 bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded-full flex items-center">
              <AlertCircle className="w-3 h-3 mr-1" />
              Rupture
            </div>
          )}

          {/* Quick Actions */}
          <motion.div
            className="absolute top-3 right-3 flex flex-col space-y-2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: isHovered ? 1 : 0, x: isHovered ? 0 : 20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.button
              onClick={handleToggleLike}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className={`p-2 rounded-full backdrop-blur-sm border border-white/20 transition-colors ${
                isLiked 
                  ? 'bg-red-500 text-white' 
                  : 'bg-white/10 text-white hover:bg-white/20'
              }`}
            >
              <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 bg-white/10 text-white rounded-full backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-colors"
            >
              <Eye className="w-4 h-4" />
            </motion.button>
          </motion.div>

          {/* Quick Add to Cart */}
          <motion.div
            className="absolute bottom-3 left-3 right-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.button
              onClick={handleAddToCart}
              disabled={isAddingToCart || !product.in_stock}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {isAddingToCart ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : inCart ? (
                <>
                  <Check className="w-4 h-4" />
                  <span>Dans le panier ({cartQuantity})</span>
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  <span>Ajouter</span>
                </>
              )}
            </motion.button>
          </motion.div>
        </div>

        {/* Product Info */}
        <div className="p-4">
          {/* Category */}
          <div className="text-xs text-primary-400 font-medium mb-1">
            {product.category?.nom}
          </div>

          {/* Product Name */}
          <h3 className="text-white font-semibold mb-2 line-clamp-2 group-hover:text-primary-300 transition-colors">
            {product.nom}
          </h3>

          {/* Rating */}
          <div className="flex items-center space-x-1 mb-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(product.average_rating || product.rating || 0)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-400'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-white/60">
              ({product.reviews_count || 0})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-white">
                {product.formatted_price || `${product.prix}€`}
              </span>
              {product.original_price && (
                <span className="text-sm text-white/50 line-through">
                  {product.original_price}€
                </span>
              )}
            </div>

            {/* Stock Indicator */}
            <div className="text-xs text-white/60">
              {product.quantite > 0 ? (
                <span className="text-green-400">En stock</span>
              ) : (
                <span className="text-red-400">Rupture</span>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default ProductCard;
