<?php

namespace App\Http\Controllers;

use App\Models\Produit;
use App\Models\Category;
use App\Models\Log;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    /**
     * Get all products with pagination and filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Produit::with(['category', 'reviews']);

            // Search filter
            if ($request->has('search') && $request->search) {
                $query->search($request->search);
            }

            // Category filter
            if ($request->has('category') && $request->category) {
                $query->where('id_categorie', $request->category);
            }

            // Price range filter
            if ($request->has('min_price')) {
                $query->where('prix', '>=', $request->min_price);
            }
            if ($request->has('max_price')) {
                $query->where('prix', '<=', $request->max_price);
            }

            // Availability filter
            if ($request->has('available') && $request->available) {
                $query->available();
            }

            // Featured filter
            if ($request->has('featured') && $request->featured) {
                $query->featured();
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'date_creation');
            $sortOrder = $request->get('sort_order', 'desc');
            
            if (in_array($sortBy, ['nom', 'prix', 'date_creation', 'rating'])) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = min($request->get('per_page', 12), 50);
            $products = $query->paginate($perPage);

            // Transform data
            $products->getCollection()->transform(function ($product) {
                return [
                    'id' => $product->id,
                    'nom' => $product->nom,
                    'description' => $product->description,
                    'prix' => $product->prix,
                    'formatted_price' => $product->formatted_price,
                    'quantite' => $product->quantite,
                    'image_url' => $product->image_url,
                    'date_creation' => $product->date_creation,
                    'featured' => $product->featured,
                    'rating' => $product->rating,
                    'average_rating' => $product->average_rating,
                    'reviews_count' => $product->reviews_count,
                    'in_stock' => $product->isInStock(),
                    'category' => [
                        'id' => $product->category->id,
                        'nom' => $product->category->nom,
                        'image_url' => $product->category->image_url,
                    ],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get featured products
     */
    public function featured(): JsonResponse
    {
        try {
            $products = Produit::with(['category', 'reviews'])
                ->featured()
                ->available()
                ->limit(8)
                ->get();

            $transformedProducts = $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'nom' => $product->nom,
                    'description' => $product->description,
                    'prix' => $product->prix,
                    'formatted_price' => $product->formatted_price,
                    'image_url' => $product->image_url,
                    'rating' => $product->rating,
                    'average_rating' => $product->average_rating,
                    'reviews_count' => $product->reviews_count,
                    'category' => [
                        'id' => $product->category->id,
                        'nom' => $product->category->nom,
                    ],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProducts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch featured products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single product
     */
    public function show($id): JsonResponse
    {
        try {
            $product = Produit::with(['category', 'reviews.user', 'images'])
                ->findOrFail($id);

            // Log product view
            if (auth()->check()) {
                Log::createLog(
                    auth()->id(),
                    'view',
                    'produits',
                    $product->id,
                    'Product viewed: ' . $product->nom
                );
            }

            // Get related products
            $relatedProducts = Produit::where('id_categorie', $product->id_categorie)
                ->where('id', '!=', $product->id)
                ->available()
                ->limit(4)
                ->get(['id', 'nom', 'prix', 'image_url', 'rating']);

            $productData = [
                'id' => $product->id,
                'nom' => $product->nom,
                'description' => $product->description,
                'prix' => $product->prix,
                'formatted_price' => $product->formatted_price,
                'quantite' => $product->quantite,
                'image_url' => $product->image_url,
                'date_creation' => $product->date_creation,
                'featured' => $product->featured,
                'rating' => $product->rating,
                'average_rating' => $product->average_rating,
                'reviews_count' => $product->reviews_count,
                'in_stock' => $product->isInStock(),
                'category' => [
                    'id' => $product->category->id,
                    'nom' => $product->category->nom,
                    'description' => $product->category->description,
                ],
                'images' => $product->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'image_url' => $image->image_url,
                        'alt_text' => $image->alt_text,
                        'is_primary' => $image->is_primary,
                    ];
                }),
                'reviews' => $product->reviews()->approved()->with('user')->latest()->take(5)->get()->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'comment' => $review->comment,
                        'star_rating' => $review->star_rating,
                        'is_verified_purchase' => $review->is_verified_purchase,
                        'created_at' => $review->created_at,
                        'time_ago' => $review->time_ago,
                        'user' => [
                            'username' => $review->user->username,
                        ],
                    ];
                }),
                'related_products' => $relatedProducts->map(function ($related) {
                    return [
                        'id' => $related->id,
                        'nom' => $related->nom,
                        'prix' => $related->prix,
                        'formatted_price' => $related->formatted_price,
                        'image_url' => $related->image_url,
                        'rating' => $related->rating,
                    ];
                }),
            ];

            return response()->json([
                'success' => true,
                'data' => $productData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Search products
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:2',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Search query is required',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $products = Produit::with('category')
                ->search($request->q)
                ->available()
                ->limit(20)
                ->get();

            $transformedProducts = $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'nom' => $product->nom,
                    'description' => substr($product->description, 0, 100) . '...',
                    'prix' => $product->prix,
                    'formatted_price' => $product->formatted_price,
                    'image_url' => $product->image_url,
                    'rating' => $product->rating,
                    'category' => $product->category->nom,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProducts,
                'count' => $products->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products by category
     */
    public function byCategory($categoryId): JsonResponse
    {
        try {
            $category = Category::findOrFail($categoryId);
            
            $products = Produit::with('category')
                ->where('id_categorie', $categoryId)
                ->available()
                ->orderBy('date_creation', 'desc')
                ->paginate(12);

            $products->getCollection()->transform(function ($product) {
                return [
                    'id' => $product->id,
                    'nom' => $product->nom,
                    'description' => $product->description,
                    'prix' => $product->prix,
                    'formatted_price' => $product->formatted_price,
                    'image_url' => $product->image_url,
                    'rating' => $product->rating,
                    'average_rating' => $product->average_rating,
                    'reviews_count' => $product->reviews_count,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'category' => [
                        'id' => $category->id,
                        'nom' => $category->nom,
                        'description' => $category->description,
                    ],
                    'products' => $products
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }
}
