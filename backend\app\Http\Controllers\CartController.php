<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Produit;
use App\Models\Log;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CartController extends Controller
{
    /**
     * Get user's cart items
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            $cartItems = Cart::with('product.category')
                ->forUser($user->id)
                ->get();

            $transformedItems = $cartItems->map(function ($item) {
                return [
                    'id' => $item->id,
                    'quantity' => $item->quantity,
                    'total_price' => $item->total_price,
                    'formatted_total' => $item->formatted_total,
                    'product' => [
                        'id' => $item->product->id,
                        'nom' => $item->product->nom,
                        'description' => $item->product->description,
                        'prix' => $item->product->prix,
                        'formatted_price' => $item->product->formatted_price,
                        'image_url' => $item->product->image_url,
                        'quantite' => $item->product->quantite,
                        'in_stock' => $item->product->isInStock(),
                        'category' => $item->product->category->nom,
                    ],
                ];
            });

            $totalAmount = $cartItems->sum('total_price');
            $totalItems = $cartItems->sum('quantity');

            return response()->json([
                'success' => true,
                'data' => [
                    'items' => $transformedItems,
                    'summary' => [
                        'total_items' => $totalItems,
                        'total_amount' => $totalAmount,
                        'formatted_total' => number_format($totalAmount, 2) . ' €',
                        'delivery_fee' => 10.00,
                        'final_total' => $totalAmount + 10.00,
                        'formatted_final_total' => number_format($totalAmount + 10.00, 2) . ' €',
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add product to cart
     */
    public function add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:produits,id',
            'quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $product = Produit::findOrFail($request->product_id);

            // Check if product is in stock
            if (!$product->isInStock() || $product->quantite < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is out of stock or insufficient quantity'
                ], 400);
            }

            // Check if item already exists in cart
            $existingItem = Cart::forUser($user->id)
                ->where('product_id', $request->product_id)
                ->first();

            if ($existingItem) {
                $newQuantity = $existingItem->quantity + $request->quantity;
                
                if ($newQuantity > $product->quantite) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot add more items than available in stock'
                    ], 400);
                }

                $existingItem->updateQuantity($newQuantity);
                $cartItem = $existingItem;
            } else {
                $cartItem = Cart::create([
                    'user_id' => $user->id,
                    'product_id' => $request->product_id,
                    'quantity' => $request->quantity,
                ]);
            }

            // Log the action
            Log::createLog(
                $user->id,
                'create',
                'cart',
                $cartItem->id,
                'Added product to cart: ' . $product->nom
            );

            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully',
                'data' => [
                    'cart_item_id' => $cartItem->id,
                    'quantity' => $cartItem->quantity,
                    'total_price' => $cartItem->total_price,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add product to cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update cart item quantity
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $cartItem = Cart::forUser($user->id)->findOrFail($id);
            $product = $cartItem->product;

            // Check stock availability
            if ($request->quantity > $product->quantite) {
                return response()->json([
                    'success' => false,
                    'message' => 'Requested quantity exceeds available stock'
                ], 400);
            }

            $cartItem->updateQuantity($request->quantity);

            // Log the action
            Log::createLog(
                $user->id,
                'update',
                'cart',
                $cartItem->id,
                'Updated cart item quantity: ' . $product->nom
            );

            return response()->json([
                'success' => true,
                'message' => 'Cart updated successfully',
                'data' => [
                    'quantity' => $cartItem->quantity,
                    'total_price' => $cartItem->total_price,
                    'formatted_total' => $cartItem->formatted_total,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove item from cart
     */
    public function remove(Request $request, $id): JsonResponse
    {
        try {
            $user = $request->user();
            $cartItem = Cart::forUser($user->id)->findOrFail($id);
            $productName = $cartItem->product->nom;

            $cartItem->delete();

            // Log the action
            Log::createLog(
                $user->id,
                'delete',
                'cart',
                $id,
                'Removed product from cart: ' . $productName
            );

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove item from cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear entire cart
     */
    public function clear(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $deletedCount = Cart::forUser($user->id)->delete();

            // Log the action
            Log::createLog(
                $user->id,
                'delete',
                'cart',
                0,
                'Cleared entire cart'
            );

            return response()->json([
                'success' => true,
                'message' => 'Cart cleared successfully',
                'data' => [
                    'deleted_items' => $deletedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cart items count
     */
    public function count(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $count = Cart::forUser($user->id)->sum('quantity');

            return response()->json([
                'success' => true,
                'data' => [
                    'count' => $count
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get cart count',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
