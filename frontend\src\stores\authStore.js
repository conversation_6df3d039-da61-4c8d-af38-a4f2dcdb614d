import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../services/api';

const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      error: null,

      // Login action
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.post('/auth/login', credentials);
          
          if (response.data.success) {
            const { user, token } = response.data.data;
            
            // Set token in API headers
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            
            set({ 
              user, 
              token, 
              isLoading: false,
              error: null 
            });
            
            return { success: true, user };
          } else {
            throw new Error(response.data.message || 'Login failed');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Login failed';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          return { success: false, error: errorMessage };
        }
      },

      // Register action
      register: async (userData) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.post('/auth/register', userData);
          
          if (response.data.success) {
            const { user, token } = response.data.data;
            
            // Set token in API headers
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            
            set({ 
              user, 
              token, 
              isLoading: false,
              error: null 
            });
            
            return { success: true, user };
          } else {
            throw new Error(response.data.message || 'Registration failed');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          return { success: false, error: errorMessage };
        }
      },

      // Logout action
      logout: async () => {
        const { token } = get();
        
        if (token) {
          try {
            await api.post('/auth/logout');
          } catch (error) {
            console.error('Logout error:', error);
          }
        }
        
        // Clear token from API headers
        delete api.defaults.headers.common['Authorization'];
        
        set({ 
          user: null, 
          token: null, 
          error: null 
        });
      },

      // Get current user
      getCurrentUser: async () => {
        const { token } = get();
        
        if (!token) {
          return { success: false, error: 'No token found' };
        }
        
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.get('/auth/me');
          
          if (response.data.success) {
            const user = response.data.data;
            set({ 
              user, 
              isLoading: false,
              error: null 
            });
            return { success: true, user };
          } else {
            throw new Error(response.data.message || 'Failed to get user data');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to get user data';
          
          // If token is invalid, logout
          if (error.response?.status === 401) {
            get().logout();
          }
          
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          return { success: false, error: errorMessage };
        }
      },

      // Refresh token
      refreshToken: async () => {
        const { token } = get();
        
        if (!token) {
          return { success: false, error: 'No token found' };
        }
        
        try {
          const response = await api.post('/auth/refresh');
          
          if (response.data.success) {
            const { token: newToken } = response.data.data;
            
            // Update token in API headers
            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
            
            set({ token: newToken });
            return { success: true, token: newToken };
          } else {
            throw new Error(response.data.message || 'Token refresh failed');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Token refresh failed';
          
          // If refresh fails, logout
          get().logout();
          
          return { success: false, error: errorMessage };
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Check if user is authenticated
      isAuthenticated: () => {
        const { user, token } = get();
        return !!(user && token);
      },

      // Check if user has specific role
      hasRole: (role) => {
        const { user } = get();
        return user?.role === role;
      },

      // Check if user is admin
      isAdmin: () => {
        const { user } = get();
        return user?.role === 'admin';
      },

      // Check if user is client
      isClient: () => {
        const { user } = get();
        return user?.role === 'client';
      },

      // Check if user is delivery
      isDelivery: () => {
        const { user } = get();
        return user?.role === 'delivery';
      },

      // Initialize auth (set token in API headers if exists)
      initialize: () => {
        const { token } = get();
        if (token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
      }),
    }
  )
);

// Initialize auth on store creation
useAuthStore.getState().initialize();

export { useAuthStore };
