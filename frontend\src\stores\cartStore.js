import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../services/api';
import toast from 'react-hot-toast';

const useCartStore = create(
  persist(
    (set, get) => ({
      items: [],
      isLoading: false,
      error: null,
      cartCount: 0,
      totalAmount: 0,

      // Calculate totals
      calculateTotals: () => {
        const { items } = get();
        const cartCount = items.reduce((total, item) => total + item.quantity, 0);
        const totalAmount = items.reduce((total, item) => total + (item.product.prix * item.quantity), 0);
        
        set({ cartCount, totalAmount });
      },

      // Fetch cart from server
      fetchCart: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.get('/cart');
          
          if (response.data.success) {
            const items = response.data.data.items || [];
            set({ 
              items, 
              isLoading: false,
              error: null 
            });
            get().calculateTotals();
            return { success: true, items };
          } else {
            throw new Error(response.data.message || 'Failed to fetch cart');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch cart';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          return { success: false, error: errorMessage };
        }
      },

      // Add item to cart
      addToCart: async (productId, quantity = 1) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.post('/cart/add', {
            product_id: productId,
            quantity
          });
          
          if (response.data.success) {
            // Refresh cart from server
            await get().fetchCart();
            
            toast.success('Produit ajouté au panier');
            return { success: true };
          } else {
            throw new Error(response.data.message || 'Failed to add to cart');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to add to cart';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      // Update cart item quantity
      updateCartItem: async (itemId, quantity) => {
        if (quantity <= 0) {
          return get().removeFromCart(itemId);
        }
        
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.put(`/cart/update/${itemId}`, {
            quantity
          });
          
          if (response.data.success) {
            // Update local state
            const { items } = get();
            const updatedItems = items.map(item => 
              item.id === itemId 
                ? { ...item, quantity, total_price: item.product.prix * quantity }
                : item
            );
            
            set({ 
              items: updatedItems, 
              isLoading: false,
              error: null 
            });
            get().calculateTotals();
            
            toast.success('Panier mis à jour');
            return { success: true };
          } else {
            throw new Error(response.data.message || 'Failed to update cart');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to update cart';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      // Remove item from cart
      removeFromCart: async (itemId) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.delete(`/cart/remove/${itemId}`);
          
          if (response.data.success) {
            // Update local state
            const { items } = get();
            const updatedItems = items.filter(item => item.id !== itemId);
            
            set({ 
              items: updatedItems, 
              isLoading: false,
              error: null 
            });
            get().calculateTotals();
            
            toast.success('Produit retiré du panier');
            return { success: true };
          } else {
            throw new Error(response.data.message || 'Failed to remove from cart');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to remove from cart';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      // Clear entire cart
      clearCart: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await api.delete('/cart/clear');
          
          if (response.data.success) {
            set({ 
              items: [], 
              cartCount: 0,
              totalAmount: 0,
              isLoading: false,
              error: null 
            });
            
            toast.success('Panier vidé');
            return { success: true };
          } else {
            throw new Error(response.data.message || 'Failed to clear cart');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to clear cart';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      // Get cart count from server
      getCartCount: async () => {
        try {
          const response = await api.get('/cart/count');
          
          if (response.data.success) {
            const count = response.data.data.count || 0;
            set({ cartCount: count });
            return { success: true, count };
          } else {
            throw new Error(response.data.message || 'Failed to get cart count');
          }
        } catch (error) {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to get cart count';
          return { success: false, error: errorMessage };
        }
      },

      // Check if product is in cart
      isInCart: (productId) => {
        const { items } = get();
        return items.some(item => item.product.id === productId);
      },

      // Get item quantity in cart
      getItemQuantity: (productId) => {
        const { items } = get();
        const item = items.find(item => item.product.id === productId);
        return item ? item.quantity : 0;
      },

      // Get cart summary
      getCartSummary: () => {
        const { items, totalAmount } = get();
        const deliveryFee = 10.00;
        const finalTotal = totalAmount + deliveryFee;
        
        return {
          itemsCount: items.length,
          totalItems: items.reduce((total, item) => total + item.quantity, 0),
          subtotal: totalAmount,
          deliveryFee,
          total: finalTotal,
          formattedSubtotal: `${totalAmount.toFixed(2)} €`,
          formattedDeliveryFee: `${deliveryFee.toFixed(2)} €`,
          formattedTotal: `${finalTotal.toFixed(2)} €`,
        };
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Reset cart (for logout)
      resetCart: () => set({
        items: [],
        cartCount: 0,
        totalAmount: 0,
        isLoading: false,
        error: null,
      }),
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items,
        cartCount: state.cartCount,
        totalAmount: state.totalAmount,
      }),
    }
  )
);

export { useCartStore };
