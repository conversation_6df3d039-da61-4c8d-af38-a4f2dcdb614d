import React from 'react';
import { motion } from 'framer-motion';

const Checkout = () => {
  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h1 className="text-3xl font-bold gradient-text mb-4">Commande</h1>
          <p className="text-white/70">Page en cours de développement...</p>
        </motion.div>
      </div>
    </div>
  );
};

export default Checkout;
