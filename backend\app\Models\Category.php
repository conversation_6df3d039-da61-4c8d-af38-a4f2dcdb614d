<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $table = 'categories';

    protected $fillable = [
        'nom',
        'description',
        'date_creation',
        'active',
        'image_url',
    ];

    protected $casts = [
        'date_creation' => 'date',
        'active' => 'boolean',
    ];

    public $timestamps = false;

    /**
     * Get the products for the category.
     */
    public function produits()
    {
        return $this->hasMany(Produit::class, 'id_categorie');
    }

    /**
     * Get active products for the category.
     */
    public function activeProducts()
    {
        return $this->hasMany(Produit::class, 'id_categorie')
                    ->where('quantite', '>', 0);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Get the category's image URL with fallback.
     */
    public function getImageUrlAttribute($value)
    {
        return $value ?: 'images/categories/default.jpg';
    }
}
