<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Retour extends Model
{
    use HasFactory;

    protected $table = 'retours';
    protected $primaryKey = 'idretour';

    protected $fillable = [
        'idcommande',
        'idproduit',
        'quantite_retournee',
        'raison',
        'date_retour',
        'statut_retour',
        'commentaire_admin',
        'montant_rembourse',
    ];

    protected $casts = [
        'quantite_retournee' => 'integer',
        'date_retour' => 'date',
        'montant_rembourse' => 'decimal:2',
    ];

    public $timestamps = false;

    /**
     * Get the order for the return.
     */
    public function commande()
    {
        return $this->belongsTo(Commande::class, 'idcommande', 'idcommande');
    }

    /**
     * Get the product for the return.
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class, 'idproduit');
    }

    /**
     * Get return status in French.
     */
    public function getStatusLabelAttribute(): string
    {
        $labels = [
            'accepte' => 'Accepté',
            'refuse' => 'Refusé',
            'en_attente' => 'En attente',
            'traite' => 'Traité',
        ];

        return $labels[$this->statut_retour] ?? $this->statut_retour;
    }

    /**
     * Check if return is accepted.
     */
    public function isAccepted(): bool
    {
        return $this->statut_retour === 'accepte';
    }

    /**
     * Check if return is rejected.
     */
    public function isRejected(): bool
    {
        return $this->statut_retour === 'refuse';
    }

    /**
     * Check if return is pending.
     */
    public function isPending(): bool
    {
        return $this->statut_retour === 'en_attente';
    }

    /**
     * Check if return is processed.
     */
    public function isProcessed(): bool
    {
        return $this->statut_retour === 'traite';
    }

    /**
     * Get formatted refund amount.
     */
    public function getFormattedRefundAttribute(): string
    {
        return $this->montant_rembourse ? 
               number_format($this->montant_rembourse, 2) . ' €' : 
               'Non défini';
    }

    /**
     * Scope a query to only include pending returns.
     */
    public function scopePending($query)
    {
        return $query->where('statut_retour', 'en_attente');
    }

    /**
     * Scope a query to only include accepted returns.
     */
    public function scopeAccepted($query)
    {
        return $query->where('statut_retour', 'accepte');
    }
}
