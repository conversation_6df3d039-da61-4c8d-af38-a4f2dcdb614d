<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;

    protected $table = 'client';
    protected $primaryKey = 'idclient';

    protected $fillable = [
        'user_id',
        'nomclient',
        'adresse',
        'tel',
        'ville',
        'code_postal',
    ];

    public $timestamps = false;

    /**
     * Get the user that owns the client profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the orders for the client.
     */
    public function commandes()
    {
        return $this->hasMany(Commande::class, 'idclient', 'idclient');
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->adresse,
            $this->ville,
            $this->code_postal
        ]);
        
        return implode(', ', $parts);
    }

    /**
     * Get recent orders.
     */
    public function recentOrders($limit = 5)
    {
        return $this->commandes()
                    ->orderBy('datecommande', 'desc')
                    ->limit($limit);
    }

    /**
     * Get total orders count.
     */
    public function getTotalOrdersAttribute(): int
    {
        return $this->commandes()->count();
    }

    /**
     * Get total spent amount.
     */
    public function getTotalSpentAttribute(): float
    {
        return $this->commandes()
                    ->where('statut', '!=', 'annule')
                    ->sum('total');
    }
}
